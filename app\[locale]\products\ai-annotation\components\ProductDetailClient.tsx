'use client'

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useTranslations, useRawTranslations } from "@/hooks/useTranslations"
import {
  ArrowRight,
  CheckCircle,
  Star,
  Shield,
  Zap,
  Globe,
  Users,
  Target,
  Database,
  Eye,
  Brain,
  Play,
  Download,
  MessageCircle,
  Calendar,
  Award,
  Clock,
  DollarSign,
  TrendingUp,
  Lock,
  Layers,
  Mic,
  FileText,
  Car,
  Heart,
  CreditCard,
  ShoppingCart,
  Camera,
  Factory,
  ArrowDown,
  CheckCircle2
} from "lucide-react"
import Link from "next/link"

interface ProductDetail {
  name: string
  description: string
  features: string[]
  techSpecs: {
    deployment: string
    security: string
    availability: string
    support: string
  }
  featureList?: Array<{
    title: string
    description: string
    features: Array<{
      name: string
      description: string
      icon: string
    }>
  }>
  demoVideo?: {
    url: string
    thumbnail: string
  }
  benefits?: Array<{
    title: string
    description: string
  }>
}

// 定义原始数据的类型
interface RawDataType {
  advantages?: Record<string, {
    title: string
    description: string
  }>
  features?: Record<string, {
    title: string
    subtitle?: string
    description: string
    services?: Record<string, string>
  }>
  industries?: {
    title?: string
    subtitle?: string
    [key: string]: {
      name: string
      description: string
    }
  }
  qualityProcess?: {
    title?: string
    subtitle?: string
    steps?: Record<string, string>
  }
  pricingInfo?: {
    title?: string
    subtitle?: string
    [key: string]: {
      name: string
      price: string
      description: string
    }
  }
}

// 类型守卫函数
const isValidAdvantage = (item: any): item is { title: string; description: string } => {
  return item && typeof item === 'object' &&
         typeof item.title === 'string' &&
         typeof item.description === 'string'
}

const isValidFeature = (item: any): item is { title: string; subtitle?: string; description: string; services?: Record<string, string> } => {
  return item && typeof item === 'object' &&
         typeof item.title === 'string' &&
         typeof item.description === 'string'
}

const isValidIndustry = (item: any): item is { name: string; description: string } => {
  return item && typeof item === 'object' &&
         typeof item.name === 'string' &&
         typeof item.description === 'string'
}

const isValidPricingPlan = (item: any): item is { name: string; price: string; description: string } => {
  return item && typeof item === 'object' &&
         typeof item.name === 'string' &&
         typeof item.price === 'string' &&
         typeof item.description === 'string'
}

interface Props {
  product: ProductDetail
  productSlug: string
}

const iconMap = {
  Target,
  Database,
  Eye,
  Brain,
  Shield,
  Zap,
  Globe,
  Users,
  CheckCircle,
  Star,
  Award,
  Clock,
  DollarSign,
  TrendingUp,
  Lock,
  Layers,
  Mic,
  FileText,
  Car,
  Heart,
  CreditCard,
  ShoppingCart,
  Camera,
  Factory,
  CheckCircle2
}

export function ProductDetailClient({ product, productSlug }: Props) {
  const t = useTranslations('productDetails.aiAnnotation')
  const tAI = useTranslations('aiAnnotation')
  const tCommon = useTranslations('common')
  const rawData = useRawTranslations('productDetails.aiAnnotation') as RawDataType

  const renderIcon = (iconName: string, className?: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap]
    return IconComponent ? <IconComponent className={className} /> : <Target className={className} />
  }

  return (
    <div className="relative isolate min-h-screen">
      {/* Background Effects */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/3 to-transparent dark:from-primary/10 dark:via-primary/5" />
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/10 dark:bg-primary/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-primary/8 dark:bg-primary/15 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      {/* Hero Section */}
      <section className="relative py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-4xl text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-6"
            >
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-primary/20 bg-primary/5">
                <Brain className="w-4 h-4 mr-2" />
                {t('badge')}
              </Badge>
            </motion.div>

            <motion.h1
              className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <span className="text-gradient-modern">{product.name}</span>
            </motion.h1>

            <motion.p
              className="text-xl leading-8 text-muted-foreground mb-8 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              {product.description}
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <Button asChild size="lg" className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
                <Link href="/contact-us">
                  {tCommon('contactUs')}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              
              <Button variant="outline" size="lg">
                <Calendar className="mr-2 h-5 w-5" />
                {tCommon('learnMore')}
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('coreFeatures')}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {t('coreDescription')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {product.features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="relative overflow-hidden border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <CheckCircle className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-lg font-semibold group-hover:text-primary transition-colors">
                        {feature}
                      </h3>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Advantages Section */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('keyAdvantages')}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {tAI('advantagesDescription')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {Object.entries(rawData.advantages || {})
              .filter(([, advantage]) => isValidAdvantage(advantage))
              .map(([key, advantage], index) => {
                const validAdvantage = advantage as { title: string; description: string }
                return (
              <motion.div
                key={key}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="relative overflow-hidden border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                        {renderIcon('Award', 'h-6 w-6 text-white')}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                          {validAdvantage.title}
                        </h3>
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {validAdvantage.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
                )
              })}
          </div>
        </div>
      </section>

      {/* Tech Specs Section */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('techSpecsTitle')}</h2>
          </motion.div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            {Object.entries(product.techSpecs).map(([key, value], index) => (
              <motion.div
                key={key}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardContent className="p-6 text-center">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg mb-4 inline-block">
                      {renderIcon('Shield', 'h-6 w-6 text-white')}
                    </div>
                    <h3 className="font-semibold mb-2">{key}</h3>
                    <p className="text-sm text-muted-foreground">{value}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Features Section */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('serviceFeatures')}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {tAI('serviceFeaturesDescription')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-12 lg:gap-16">
            {Object.entries(rawData.features || {})
              .filter(([, feature]) => isValidFeature(feature))
              .map(([key, feature], index) => {
                const validFeature = feature as { title: string; subtitle?: string; description: string; services?: Record<string, string> }
                return (
              <motion.div
                key={key}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="relative"
              >
                <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500">
                  <CardContent className="p-8">
                    <div className="flex flex-col lg:flex-row gap-8">
                      <div className="lg:w-1/3">
                        <div className="flex items-center gap-4 mb-4">
                          <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg">
                            {key === 'computerVision' && <Eye className="h-6 w-6 text-white" />}
                            {key === 'naturalLanguageProcessing' && <FileText className="h-6 w-6 text-white" />}
                            {key === 'speechProcessing' && <Mic className="h-6 w-6 text-white" />}
                          </div>
                          <div>
                            <h3 className="text-xl font-bold">{validFeature.title}</h3>
                            <p className="text-sm text-muted-foreground">{validFeature.subtitle}</p>
                          </div>
                        </div>
                        <p className="text-muted-foreground mb-6">{validFeature.description}</p>
                      </div>

                      <div className="lg:w-2/3">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {Object.entries(validFeature.services || {}).map(([serviceKey, serviceName], serviceIndex) => (
                            <motion.div
                              key={serviceKey}
                              initial={{ opacity: 0, x: 20 }}
                              whileInView={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.4, delay: serviceIndex * 0.1 }}
                              viewport={{ once: true }}
                              className="flex items-center gap-3 p-3 rounded-lg bg-muted/50 hover:bg-muted/70 transition-colors"
                            >
                              <CheckCircle2 className="h-4 w-4 text-primary flex-shrink-0" />
                              <span className="text-sm font-medium">{serviceName}</span>
                            </motion.div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
                )
              })}
          </div>
        </div>
      </section>

      {/* Industries Section */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{rawData.industries?.title}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {rawData.industries?.subtitle}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {Object.entries(rawData.industries || {})
              .filter(([key]) => key !== 'title' && key !== 'subtitle')
              .filter(([, industry]) => isValidIndustry(industry))
              .map(([key, industry], index) => {
                const validIndustry = industry as { name: string; description: string }
                return (
              <motion.div
                key={key}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                        {key === 'automotive' && <Car className="h-6 w-6 text-white" />}
                        {key === 'healthcare' && <Heart className="h-6 w-6 text-white" />}
                        {key === 'finance' && <CreditCard className="h-6 w-6 text-white" />}
                        {key === 'retail' && <ShoppingCart className="h-6 w-6 text-white" />}
                        {key === 'security' && <Camera className="h-6 w-6 text-white" />}
                        {key === 'manufacturing' && <Factory className="h-6 w-6 text-white" />}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                          {industry?.name}
                        </h3>
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {industry.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Quality Process Section */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{rawData.qualityProcess?.title}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {rawData.qualityProcess?.subtitle}
            </p>
          </motion.div>

          <div className="flex flex-col lg:flex-row items-center justify-center gap-8">
            {Object.entries(rawData.qualityProcess?.steps || {}).map(([key, step], index) => (
              <motion.div
                key={key}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex flex-col items-center text-center group"
              >
                <div className="relative">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-primary to-primary/80 shadow-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-lg">{index + 1}</span>
                  </div>
                  {index < Object.keys(rawData.qualityProcess?.steps || {}).length - 1 && (
                    <ArrowDown className="absolute -right-8 top-1/2 transform -translate-y-1/2 h-6 w-6 text-muted-foreground hidden lg:block" />
                  )}
                </div>
                <h3 className="font-semibold mb-2 group-hover:text-primary transition-colors">
                  {step}
                </h3>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{rawData.pricingInfo?.title}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {rawData.pricingInfo?.subtitle}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {Object.entries(rawData.pricingInfo || {})
              .filter(([key]) => key !== 'title' && key !== 'subtitle')
              .filter(([, plan]) => isValidPricingPlan(plan))
              .map(([key, plan], index) => {
                const validPlan = plan as { name: string; price: string; description: string }
                return (
              <motion.div
                key={key}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className={`border-0 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full ${
                  key === 'professional'
                    ? 'bg-gradient-to-br from-primary/10 to-primary/5 ring-2 ring-primary/20'
                    : 'bg-background/60 dark:bg-background/40'
                }`}>
                  <CardContent className="p-8 text-center">
                    <div className="mb-6">
                      <h3 className="text-xl font-bold mb-2">{validPlan.name}</h3>
                      <div className="text-3xl font-bold text-primary mb-2">{validPlan.price}</div>
                      <p className="text-sm text-muted-foreground">{validPlan.description}</p>
                    </div>
                    <Button
                      className={`w-full ${
                        key === 'professional'
                          ? 'bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70'
                          : ''
                      }`}
                      variant={key === 'professional' ? 'default' : 'outline'}
                    >
                      {key === 'enterprise' ? tAI('contactConsultation') : tAI('getStarted')}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
                )
              })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-br from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20">
        <div className="mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('readyToStart')}</h2>
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              {t('ctaDescription')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
                <MessageCircle className="w-5 h-5 mr-2" />
                {tCommon('contactUs')}
              </Button>
              <Button variant="outline" size="lg">
                <Download className="w-5 h-5 mr-2" />
                {t('downloadBrochure')}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
